// 预加载脚本，暴露与主进程通信的API
const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露安全的 API
contextBridge.exposeInMainWorld('electronAPI', {
    // 保存配置
    saveConfig: (config) => ipcRenderer.invoke('save-config', config),

    // 加载配置
    loadConfig: () => ipcRenderer.invoke('load-config'),

    // 执行关机
    shutdown: () => ipcRenderer.invoke('shutdown'),

    // 获取系统信息
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),



    // 隐藏到托盘
    hideToTray: () => ipcRenderer.invoke('hide-to-tray')
});
