# 资源文件说明

## 图标文件

为了获得最佳的用户体验，请在此文件夹中放置以下图标文件：

### 1. 应用图标 (icon.png)
- **文件名**: `icon.png`
- **推荐尺寸**: 256x256 像素
- **格式**: PNG
- **用途**: 应用窗口图标、任务栏图标

### 2. 托盘图标 (tray-icon.png)
- **文件名**: `tray-icon.png`
- **推荐尺寸**: 16x16 或 32x32 像素
- **格式**: PNG
- **用途**: 系统托盘图标
- **注意**: 建议使用简单的单色图标，在不同主题下都能清晰显示

## 图标设计建议

1. **托盘图标**:
   - 使用简单的几何形状
   - 避免过于复杂的细节
   - 考虑深色和浅色主题的兼容性
   - 可以使用电源符号、时钟符号等相关图标

2. **应用图标**:
   - 可以更加详细和丰富
   - 体现应用的功能特点
   - 保持与操作系统风格的一致性

## 如果没有图标文件

如果没有提供图标文件，应用仍然可以正常运行：
- 应用图标会使用 Electron 默认图标
- 托盘图标会使用空图标（可能不可见）

建议至少提供托盘图标以确保托盘功能的可用性。

## 创建简单图标的方法

可以使用以下工具创建图标：
1. **在线工具**: Canva、Figma 等
2. **桌面软件**: GIMP、Photoshop、Paint.NET 等
3. **图标库**: Feather Icons、Material Icons 等
4. **AI 生成**: 使用 AI 工具生成简单图标
