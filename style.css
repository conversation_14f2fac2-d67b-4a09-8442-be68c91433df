/* CSS 变量：定义颜色和间距，实现极简风格 */
:root {
    --theme-color: #5c6bc0; /* 主题色：柔和的蓝灰色，用于强调元素 */
    --background-color: #f7f8fa; /* 整体背景色 */
    --card-background: #ffffff; /* 卡片背景色 */
    --text-dark: #333333; /* 主要文本颜色 */
    --text-light: #666666; /* 次要文本颜色 */
    --border-light: #eeeeee; /* 边框颜色 */
    --success-color: #66bb6a; /* 成功提示色 */
    --danger-color: #ef5350; /* 危险/错误提示色 */
    --border-radius: 4px; /* 统一圆角 */
    --spacing-xs: 8px; /* 极小间距 */
    --spacing-sm: 12px; /* 小间距 */
    --spacing-md: 20px; /* 中间距 */
    --spacing-lg: 32px; /* 大间距 */
}

/* 全局重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: var(--background-color);
    min-height: 100vh; /* 确保占据整个视口高度 */
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中内容 */
    padding: var(--spacing-md); /* 整体内边距，略为收敛 */
    color: var(--text-dark);
    line-height: 1.6;
    text-rendering: optimizeLegibility; /* 字体渲染优化 */
}

.container {
    max-width: 700px; /* 调整最大宽度，更宽敞但仍保持集中 */
    width: 100%;
    margin: 0 auto;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.header h1 {
    font-size: 1.8rem;
    margin-bottom: var(--spacing-xs);
    color: var(--theme-color); /* 标题使用主题色 */
    font-weight: 600; /* 略粗字体 */
}

.header p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* 当前时间显示 */
.current-time {
    text-align: center;
    color: var(--text-light);
    font-size: 1rem; /* 略大一点 */
    margin-bottom: var(--spacing-md);
    font-weight: bold;
}

/* 卡片通用样式 */
.card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    border: 1px solid var(--border-light); /* 细边框保持极简 */
}

.card-header {
    background-color: var(--theme-color); /* 卡片头部使用主题色 */
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.95rem;
    font-weight: bold;
}

.card-body {
    padding: var(--spacing-md);
}

/* 计划项样式 */
.schedule-item {
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--card-background);
    transition: border-color 0.1s ease-in-out;
}

.schedule-item:last-child {
    margin-bottom: 0;
}

.schedule-item.active {
    border-color: var(--theme-color); /* 活跃状态边框使用主题色 */
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.schedule-title {
    font-weight: 500; /* 正常字重 */
    font-size: 0.9rem;
    color: var(--text-dark);
}

/* 时间输入框组 */
.time-inputs {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
    align-items: center;
}

.time-input {
    width: 48px; /* 略宽以适应两位数 */
    padding: 6px;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    text-align: center;
    font-size: 0.95rem; /* 略大一点 */
    font-weight: bold;
    color: var(--text-dark);
    transition: border-color 0.1s;
}

.time-input:focus {
    border-color: var(--theme-color);
    outline: none;
}

.time-label {
    font-weight: bold;
    color: var(--text-light);
    font-size: 0.95rem;
}

/* 星期选择 */
.days-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr)); /* 调整网格单元大小 */
    gap: 4px;
}

.day-item {
    text-align: center;
}

.day-checkbox {
    display: none;
}

.day-label {
    display: block;
    padding: 6px 0;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.1s;
    font-weight: normal;
    background: var(--background-color); /* 使用主题背景色，更统一 */
    color: var(--text-light);
    font-size: 0.8rem;
}

.day-checkbox:checked + .day-label {
    background: var(--theme-color);
    color: white;
    border-color: var(--theme-color);
}

.day-label:hover {
    border-color: var(--theme-color);
    background: #e8eaf6; /* 主题色略浅的悬停效果 */
}

/* 控制按钮组 */
.controls {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
    padding: var(--spacing-md);
    background-color: var(--background-color); /* 使用主题背景色 */
    border-top: 1px solid var(--border-light);
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: bold;
    transition: all 0.1s;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 按钮颜色 */
.btn-primary {
    background: var(--theme-color);
    color: white;
}
.btn-primary:hover {
    background: #4f5aab; /* 略深 */
}

.btn-secondary {
    background: #9e9e9e; /* 中性灰色 */
    color: white;
}
.btn-secondary:hover {
    background: #8e8e8e; /* 略深 */
}

.delete-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 5px 9px;
    cursor: pointer;
    font-size: 0.75rem;
    transition: background 0.1s;
}
.delete-btn:hover {
    background: #e54747; /* 略深 */
}

/* 状态栏 */
.status-bar {
    background-color: #bdbdbd; /* 默认中性灰 */
    color: white;
    padding: var(--spacing-sm);
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.status-bar.success {
    background-color: var(--success-color); /* 成功状态 */
}

.status-bar.error {
    background-color: var(--danger-color); /* 错误状态 */
}

.status-bar.stopped {
    background-color: #bdbdbd; /* 停止状态（默认） */
}

/* 添加计划按钮 */
.add-schedule {
    text-align: center;
    padding: var(--spacing-md);
    border: 1px dashed var(--border-light); /* 更细的虚线 */
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.1s;
    color: var(--text-light);
    background: var(--background-color); /* 使用主题背景色 */
}

.add-schedule:hover {
    border-color: var(--theme-color);
    color: var(--theme-color);
    background: #e8eaf6; /* 主题色略浅的悬停效果 */
}

.add-schedule div:first-child {
    font-size: 1.3rem; /* 略小加号 */
    margin-bottom: 4px;
}

.icon {
    font-size: 0.9rem; /* 略小图标 */
}
