// 存储所有关机计划的数组
let schedules = [];
// 用于生成唯一ID的计数器
let scheduleIdCounter = 1;

// 星期名称数组
const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

// 页面加载完成后执行初始化
document.addEventListener('DOMContentLoaded', () => {
    loadSchedules(); // 加载已保存的计划
    updateCurrentTime(); // 立即更新当前时间显示
    setInterval(updateCurrentTime, 1000); // 每秒更新一次时间
});

// 更新当前时间显示
function updateCurrentTime() {
    const now = new Date();
    // 获取小时、分钟、秒并补足两位
    const hh = String(now.getHours()).padStart(2, '0');
    const mm = String(now.getMinutes()).padStart(2, '0');
    const ss = String(now.getSeconds()).padStart(2, '0');

    const timeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'long'
    }) + ` ${hh}:${mm}:${ss}`; // 拼接完整时间字符串
    document.getElementById('currentTime').textContent = `当前时间: ${timeStr}`;
}

// 添加新的关机计划
function addNewSchedule() {
    const newSchedule = {
        id: scheduleIdCounter++, // 分配一个唯一的ID
        hh: 0, // 小时
        mm: 0, // 分钟
        ss: 0, // 秒
        days: [] // 选中的星期，用数字表示 (0-6)
    };
    schedules.push(newSchedule); // 将新计划添加到数组
    renderSchedules(); // 重新渲染计划列表
    updateStatusDisplay(); // 更新状态栏显示
}

// 删除指定ID的关机计划
function deleteSchedule(id) {
    schedules = schedules.filter(s => s.id !== id); // 过滤掉要删除的计划
    renderSchedules(); // 重新渲染计划列表
    updateStatusDisplay(); // 更新状态栏显示
}

// 渲染所有关机计划到页面
function renderSchedules() {
    const container = document.getElementById('schedulesList');
    container.innerHTML = ''; // 清空现有内容

    schedules.forEach((schedule, index) => {
        const scheduleDiv = document.createElement('div');
        scheduleDiv.className = 'schedule-item';
        // 使用模板字符串构建HTML结构
        scheduleDiv.innerHTML = `
            <div class="schedule-header">
                <div class="schedule-title">关机计划 ${index + 1}</div>
                <button class="delete-btn" onclick="deleteSchedule(${schedule.id})">删除</button>
            </div>

            <div class="time-inputs">
                <span class="time-label">时间:</span>
                <input type="number" class="time-input" min="0" max="23" value="${String(schedule.hh).padStart(2, '0')}"
                       onchange="updateScheduleTime(this, ${schedule.id}, 'hh', this.value)">
                <span class="time-label">:</span>
                <input type="number" class="time-input" min="0" max="59" value="${String(schedule.mm).padStart(2, '0')}"
                       onchange="updateScheduleTime(this, ${schedule.id}, 'mm', this.value)">
                <span class="time-label">:</span>
                <input type="number" class="time-input" min="0" max="59" value="${String(schedule.ss).padStart(2, '0')}"
                       onchange="updateScheduleTime(this, ${schedule.id}, 'ss', this.value)">
            </div>

            <div class="days-container">
                ${dayNames.map((dayName, dayIndex) => `
                    <div class="day-item">
                        <input type="checkbox" class="day-checkbox" id="day_${schedule.id}_${dayIndex}"
                               ${schedule.days.includes(dayIndex) ? 'checked' : ''}
                               onchange="updateScheduleDays(${schedule.id}, ${dayIndex}, this.checked)">
                        <label class="day-label" for="day_${schedule.id}_${dayIndex}">${dayName}</label>
                    </div>
                `).join('')}
            </div>
        `;
        container.appendChild(scheduleDiv); // 添加到列表中
    });
}

// 更新计划的时间字段（小时、分钟、秒）并进行合法性验证和格式化
function updateScheduleTime(inputElement, id, field, value) {
    const schedule = schedules.find(s => s.id === id);
    if (schedule) {
        let parsedValue = parseInt(value) || 0; // 转换为整数，如果无效则为0

        // 根据字段进行值范围验证
        if (field === 'hh') {
            parsedValue = Math.max(0, Math.min(23, parsedValue)); // 小时范围0-23
        } else if (field === 'mm' || field === 'ss') {
            parsedValue = Math.max(0, Math.min(59, parsedValue)); // 分钟和秒范围0-59
        }

        schedule[field] = parsedValue; // 更新计划对象中的值
        inputElement.value = String(parsedValue).padStart(2, '0'); // 更新输入框显示为两位数
    }
}

// 更新计划的选中星期
function updateScheduleDays(id, dayIndex, checked) {
    const schedule = schedules.find(s => s.id === id);
    if (schedule) {
        if (checked) {
            // 如果选中且未包含，则添加
            if (!schedule.days.includes(dayIndex)) {
                schedule.days.push(dayIndex);
            }
        } else {
            // 如果未选中且已包含，则移除
            schedule.days = schedule.days.filter(d => d !== dayIndex);
        }
    }
}

// 保存所有计划到本地存储或Electron API
async function saveAllSchedules() {
    try {
        if (window.electronAPI) { // 如果是Electron环境
            await window.electronAPI.saveConfig(schedules);
            showStatus('配置已保存', 'success');
        } else { // 浏览器环境
            localStorage.setItem('shutdownSchedules', JSON.stringify(schedules));
            showStatus('配置已保存到本地存储', 'success');
        }
    } catch (error) {
        console.error('保存失败:', error);
        showStatus('保存失败', 'error');
    }
}

// 从本地存储或Electron API加载计划
async function loadSchedules() {
    try {
        let loadedSchedules;
        if (window.electronAPI) { // 如果是Electron环境
            loadedSchedules = await window.electronAPI.loadConfig();
        } else { // 浏览器环境
            const stored = localStorage.getItem('shutdownSchedules');
            loadedSchedules = stored ? JSON.parse(stored) : [];
        }

        if (loadedSchedules && loadedSchedules.length > 0) {
            // 确保加载的计划有ID，并更新ID计数器
            schedules = loadedSchedules.map((s, index) => ({
                ...s,
                id: s.id || (index + 1)
            }));
            scheduleIdCounter = Math.max(...schedules.map(s => s.id)) + 1;
        } else {
            schedules = [];
            scheduleIdCounter = 1;
        }

        renderSchedules(); // 渲染加载的计划
        updateStatusDisplay(); // 更新状态栏
        showStatus('配置已加载', 'success');
    } catch (error) {
        console.error('加载失败:', error);
        showStatus('加载失败', 'error');
    }
}

// 更新状态栏的文本和样式
function updateStatusDisplay() {
    const statusBar = document.getElementById('statusBar');
    statusBar.className = 'status-bar stopped'; // 默认设置为停止状态样式
    statusBar.textContent = schedules.length > 0 ?
        `已配置 ${schedules.length} 个关机计划` :
        '准备就绪 - 请添加关机计划';
}

// 隐藏应用到系统托盘 (仅Electron环境支持)
async function hideToTray() {
    try {
        if (window.electronAPI) {
            await window.electronAPI.hideToTray();
            showStatus('已隐藏到系统托盘', 'success');
        } else {
            showStatus('浏览器环境不支持托盘功能', 'error');
        }
    } catch (error) {
        console.error('隐藏到托盘失败:', error);
        showStatus('隐藏到托盘失败', 'error');
    }
}

// 显示短暂的状态信息
function showStatus(message, type = 'info') {
    const statusBar = document.getElementById('statusBar');
    const originalText = statusBar.textContent;
    const originalClass = statusBar.className; // 保存原始类名

    // 临时显示状态信息
    statusBar.textContent = message;
    // 移除旧的状态类，添加新的状态类
    statusBar.classList.remove('success', 'error', 'stopped'); 
    statusBar.classList.add(type);

    // 3秒后恢复原状态
    setTimeout(() => {
        statusBar.textContent = originalText;
        statusBar.className = originalClass; // 恢复原始类名
    }, 3000);
}
